<script setup>
import { Link } from '@inertiajs/vue3';
import AppLayout from '@/Layouts/AppLayout.vue';
import { ref, watch, computed, onMounted } from 'vue';
import debounce from 'lodash/debounce';
import { router } from '@inertiajs/vue3';
import HBWTableModal from '@/Components/HBWTableModal.vue';
import TeileModal from '@/Components/TeileModal.vue';
import { Chart as ChartJS, ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, LineElement, PointElement } from 'chart.js';
import { Doughnut, Bar, Line } from 'vue-chartjs';

ChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, LineElement, PointElement);

const props = defineProps({
    rotekarten: {
        type: Object,
        required: true
    },
    filters: {
        type: Object,
        default: () => ({
            search: '',
            status: '',
            abteilung: '',
            date_from: '',
            date_to: ''
        })
    },
    totalCounts: {
        type: Object,
        required: true
    },
    qsPrueferStats: {
        type: Array,
        required: true
    }
});

// Dashboard Statistiken
const statistics = computed(() => {
    return {
        total: props.totalCounts.total,
        offen: props.totalCounts.offen,
        inBearbeitung: props.totalCounts.inBearbeitung,
        abgeschlossen: props.totalCounts.abgeschlossen,
        kleinguss: props.totalCounts.kleinguss,
        grossguss: props.totalCounts.grossguss
    };
});

// Erweiterte Statistiken
const extendedStats = computed(() => {
    const total = statistics.value.total;
    return {
        completionRate: total > 0 ? Math.round((statistics.value.abgeschlossen / total) * 100) : 0,
        inProgressRate: total > 0 ? Math.round((statistics.value.inBearbeitung / total) * 100) : 0,
        openRate: total > 0 ? Math.round((statistics.value.offen / total) * 100) : 0,
        kleingussRate: total > 0 ? Math.round((statistics.value.kleinguss / total) * 100) : 0,
        grossgussRate: total > 0 ? Math.round((statistics.value.grossguss / total) * 100) : 0
    };
});

// Animierte Zahlen
const animatedStats = ref({
    total: 0,
    offen: 0,
    inBearbeitung: 0,
    abgeschlossen: 0
});

// Animation für Statistiken
const animateNumbers = () => {
    const duration = 1000;
    const steps = 60;
    const stepDuration = duration / steps;

    const targets = {
        total: statistics.value.total,
        offen: statistics.value.offen,
        inBearbeitung: statistics.value.inBearbeitung,
        abgeschlossen: statistics.value.abgeschlossen
    };

    let currentStep = 0;

    const animate = () => {
        currentStep++;
        const progress = currentStep / steps;
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);

        Object.keys(targets).forEach(key => {
            animatedStats.value[key] = Math.round(targets[key] * easeOutQuart);
        });

        if (currentStep < steps) {
            setTimeout(animate, stepDuration);
        }
    };

    animate();
};

onMounted(() => {
    animateNumbers();
});

// Chart Data für Status-Verteilung
const statusChartData = computed(() => ({
    labels: ['Offen', 'In Bearbeitung', 'Abgeschlossen'],
    datasets: [{
        data: [statistics.value.offen, statistics.value.inBearbeitung, statistics.value.abgeschlossen],
        backgroundColor: ['#FCD34D', '#60A5FA', '#34D399'],
        borderWidth: 0
    }]
}));

// Chart Data für Abteilungsverteilung
const abteilungChartData = computed(() => ({
    labels: ['Kleinguss', 'Großguss'],
    datasets: [{
        data: [statistics.value.kleinguss, statistics.value.grossguss],
        backgroundColor: ['#8B5CF6', '#EC4899'],
        borderWidth: 0
    }]
}));

// Chart Data für QS-Prüfer-Verteilung
const qsPrueferChartData = computed(() => ({
    labels: props.qsPrueferStats.map(stat => stat.name),
    datasets: [{
        data: props.qsPrueferStats.map(stat => stat.anzahl),
        backgroundColor: [
            '#8B5CF6', '#EC4899', '#F59E0B', '#10B981', '#3B82F6',
            '#6366F1', '#14B8A6', '#84CC16', '#EF4444', '#06B6D4'
        ],
        borderWidth: 0
    }]
}));

const qsPrueferChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'bottom',
            labels: {
                boxWidth: 12,
                padding: 15
            }
        },
        tooltip: {
            callbacks: {
                label: (context) => {
                    const value = context.raw;
                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                    const percentage = ((value / total) * 100).toFixed(1);
                    return `${context.label}: ${value} (${percentage}%)`;
                }
            }
        },
        datalabels: {
            color: '#fff',
            font: {
                weight: 'bold',
                size: 11
            },
            formatter: (value, context) => {
                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                const percentage = ((value / total) * 100).toFixed(0);
                return `${percentage}%`;
            }
        }
    }
};

const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'bottom',
            labels: {
                boxWidth: 12,
                padding: 15,
                font: {
                    size: 12,
                    family: 'Inter, system-ui, sans-serif'
                }
            }
        },
        tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#fff',
            bodyColor: '#fff',
            borderColor: 'rgba(255, 255, 255, 0.1)',
            borderWidth: 1,
            cornerRadius: 8,
            displayColors: true,
            callbacks: {
                label: (context) => {
                    const value = context.raw;
                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                    const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                    return `${context.label}: ${value} (${percentage}%)`;
                }
            }
        }
    },
    animation: {
        animateRotate: true,
        animateScale: true,
        duration: 1000,
        easing: 'easeOutQuart'
    }
};

const search = ref(props.filters.search);
const selectedStatus = ref(props.filters.status);
const selectedAbteilung = ref(props.filters.abteilung);
const dateFrom = ref(props.filters.date_from);
const dateTo = ref(props.filters.date_to);

const statusOptions = [
    { value: '', label: 'Alle Status' },
    { value: 'Offen', label: 'Offen' },
    { value: 'In Bearbeitung', label: 'In Bearbeitung' },
    { value: 'Abgeschlossen', label: 'Abgeschlossen' },
    //{ value: 'Abgelehnt', label: 'Abgelehnt' }
];

const abteilungOptions = [
    { value: '', label: 'Alle Abteilungen' },
    { value: 'NG', label: 'Kleinguss' },
    { value: 'GG', label: 'Großguss' },
    { value: 'HF', label: 'Handformerei' }
];

const debouncedSearch = debounce(() => {
    router.get(route('dashboard'), {
        search: search.value,
        status: selectedStatus.value,
        abteilung: selectedAbteilung.value,
        date_from: dateFrom.value,
        date_to: dateTo.value
    }, {
        preserveState: true,
        preserveScroll: true,
        replace: true
    });
}, 300);

const clearSearch = () => {
    search.value = '';
    debouncedSearch();
};

watch([search, selectedStatus, selectedAbteilung, dateFrom, dateTo], () => {
    debouncedSearch();
});

const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}.${month}.${year}`;
};

const resetFilters = () => {
    search.value = '';
    selectedStatus.value = '';
    selectedAbteilung.value = '';
    dateFrom.value = '';
    dateTo.value = '';

    router.get(route('dashboard'), {}, { preserveState: true, preserveScroll: true });
};

const showHBWTable = ref(false);
const showTeileModal = ref(false);
const selectedTeile = ref([]);

const openTeileModal = (teile) => {
    selectedTeile.value = teile || [];
    showTeileModal.value = true;
};
</script>

<template>
    <AppLayout>
        <div class="max-w-7xl mx-auto animate-fade-in">
            <!-- Modern Hero Section -->
            <div class="relative overflow-hidden bg-gradient-to-br from-indigo-50 via-white to-purple-50 rounded-2xl shadow-xl mb-8 animate-slide-up">
                <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>
                <div class="relative p-8">
                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div class="flex-1">
                            <div class="flex items-center gap-4 mb-4">
                                <div class="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl shadow-lg">
                                    <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                    </svg>
                                </div>
                                <div>
                                    <h1 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                                        Rotekarten Dashboard
                                    </h1>
                                    <p class="text-lg text-gray-600 mt-1">Intelligente Qualitätskontrolle im Überblick</p>
                                </div>
                            </div>
                            <p class="text-gray-600 max-w-2xl">
                                Verwalten Sie alle Rotekarten effizient an einem Ort. Behalten Sie den Überblick über Status,
                                Abteilungen und Qualitätsprüfungen mit modernen Visualisierungen und intelligenten Filtern.
                            </p>
                        </div>

                        <!-- Quick Actions -->
                        <div class="mt-6 lg:mt-0 lg:ml-8">
                            <div class="flex flex-col sm:flex-row gap-3">
                                <button
                                    @click="showHBWTable = true"
                                    class="inline-flex items-center px-6 py-3 border border-gray-200 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200"
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M3 3v18h18"></path>
                                        <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"></path>
                                    </svg>
                                    HBW-Tabelle
                                </button>
                                <Link
                                    :href="route('spektrometer.index')"
                                    class="inline-flex items-center px-6 py-3 border border-transparent rounded-xl shadow-lg text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:scale-105"
                                >
                                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    Neue Rotekarte erstellen
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 animate-scale-in">
                <!-- Gesamtanzahl -->
                <div class="group relative overflow-hidden bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-400 to-blue-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative z-10">
                        <div class="flex items-center justify-between mb-4">
                            <div class="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                                <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                            </div>
                            <div class="text-right">
                                <div class="text-3xl font-bold">{{ animatedStats.total }}</div>
                                <div class="text-blue-100 text-sm font-medium">Gesamt Rotekarten</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-blue-100 text-sm">Alle Karten</span>
                            <div class="flex items-center text-blue-100">
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                </svg>
                                <span class="text-xs">100%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Offene Karten -->
                <Link :href="route('offene-karten-druck')" class="group relative overflow-hidden bg-gradient-to-br from-amber-500 to-orange-600 rounded-2xl p-6 text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="absolute inset-0 bg-gradient-to-br from-amber-400 to-orange-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative z-10">
                        <div class="flex items-center justify-between mb-4">
                            <div class="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                                <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="text-right">
                                <div class="text-3xl font-bold">{{ animatedStats.offen }}</div>
                                <div class="text-amber-100 text-sm font-medium">Offene Karten</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-amber-100 text-sm">Wartend</span>
                            <div class="flex items-center text-amber-100">
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                </svg>
                                <span class="text-xs">{{ extendedStats.openRate }}%</span>
                            </div>
                        </div>
                    </div>
                </Link>

                <!-- In Bearbeitung -->
                <Link :href="route('in-bearbeitung-karten-druck')" class="group relative overflow-hidden bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl p-6 text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="absolute inset-0 bg-gradient-to-br from-purple-400 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative z-10">
                        <div class="flex items-center justify-between mb-4">
                            <div class="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                                <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                            </div>
                            <div class="text-right">
                                <div class="text-3xl font-bold">{{ animatedStats.inBearbeitung }}</div>
                                <div class="text-purple-100 text-sm font-medium">In Bearbeitung</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-purple-100 text-sm">Aktiv</span>
                            <div class="flex items-center text-purple-100">
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                </svg>
                                <span class="text-xs">{{ extendedStats.inProgressRate }}%</span>
                            </div>
                        </div>
                    </div>
                </Link>

                <!-- Abgeschlossen -->
                <div class="group relative overflow-hidden bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl p-6 text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="absolute inset-0 bg-gradient-to-br from-emerald-400 to-green-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative z-10">
                        <div class="flex items-center justify-between mb-4">
                            <div class="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                                <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                            <div class="text-right">
                                <div class="text-3xl font-bold">{{ animatedStats.abgeschlossen }}</div>
                                <div class="text-emerald-100 text-sm font-medium">Abgeschlossen</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-emerald-100 text-sm">Erledigt</span>
                            <div class="flex items-center text-emerald-100">
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                </svg>
                                <span class="text-xs">{{ extendedStats.completionRate }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Analytics Section -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <!-- Status-Verteilung -->
                <div class="bg-white rounded-2xl shadow-xl p-6 border border-gray-100">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Status-Verteilung</h3>
                        <div class="p-2 bg-blue-50 rounded-lg">
                            <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                    </div>
                    <div class="h-64 mb-4">
                        <Doughnut :data="statusChartData" :options="chartOptions" />
                    </div>
                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div class="p-3 bg-yellow-50 rounded-lg">
                            <div class="text-lg font-bold text-yellow-600">{{ statistics.offen }}</div>
                            <div class="text-xs text-yellow-600">Offen</div>
                        </div>
                        <div class="p-3 bg-blue-50 rounded-lg">
                            <div class="text-lg font-bold text-blue-600">{{ statistics.inBearbeitung }}</div>
                            <div class="text-xs text-blue-600">Bearbeitung</div>
                        </div>
                        <div class="p-3 bg-green-50 rounded-lg">
                            <div class="text-lg font-bold text-green-600">{{ statistics.abgeschlossen }}</div>
                            <div class="text-xs text-green-600">Erledigt</div>
                        </div>
                    </div>
                </div>

                <!-- Abteilungs-Verteilung -->
                <div class="bg-white rounded-2xl shadow-xl p-6 border border-gray-100">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Abteilungs-Verteilung</h3>
                        <div class="p-2 bg-purple-50 rounded-lg">
                            <svg class="h-5 w-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                        </div>
                    </div>
                    <div class="h-64 mb-4">
                        <Doughnut :data="abteilungChartData" :options="chartOptions" />
                    </div>
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div class="p-3 bg-purple-50 rounded-lg">
                            <div class="text-lg font-bold text-purple-600">{{ statistics.kleinguss }}</div>
                            <div class="text-xs text-purple-600">Kleinguss</div>
                            <div class="text-xs text-gray-500">{{ extendedStats.kleingussRate }}%</div>
                        </div>
                        <div class="p-3 bg-pink-50 rounded-lg">
                            <div class="text-lg font-bold text-pink-600">{{ statistics.grossguss }}</div>
                            <div class="text-xs text-pink-600">Großguss</div>
                            <div class="text-xs text-gray-500">{{ extendedStats.grossgussRate }}%</div>
                        </div>
                    </div>
                </div>

                <!-- QS-Prüfer-Verteilung -->
                <div class="bg-white rounded-2xl shadow-xl p-6 border border-gray-100">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">QS-Prüfer-Verteilung</h3>
                        <div class="p-2 bg-green-50 rounded-lg">
                            <svg class="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                    </div>
                    <div class="h-64 mb-4">
                        <Doughnut
                            :data="qsPrueferChartData"
                            :options="qsPrueferChartOptions"
                        />
                    </div>
                    <div class="text-center p-4 bg-gray-50 rounded-lg">
                        <div class="text-2xl font-bold text-gray-900">
                            {{ props.qsPrueferStats.reduce((sum, stat) => sum + stat.anzahl, 0) }}
                        </div>
                        <div class="text-sm text-gray-600">Gesamt QS-Prüfungen</div>
                        <div class="text-xs text-gray-500 mt-1">
                            {{ props.qsPrueferStats.length }} aktive Prüfer
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Search and Filter Section -->
            <div class="bg-white rounded-2xl shadow-xl p-6 mb-8 border border-gray-100">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-semibold text-gray-900">Rotekarten durchsuchen & filtern</h2>
                    <div class="flex items-center space-x-2">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {{ rotekarten.total }} Ergebnisse
                        </span>
                        <Link :href="route('statistik.index')" class="inline-flex items-center px-4 py-2 text-sm font-medium text-indigo-600 hover:text-indigo-700 transition-colors">
                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                            Statistiken
                        </Link>
                    </div>
                </div>

                <!-- Enhanced Search Field -->
                <div class="relative mb-6">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <input
                            type="text"
                            v-model="search"
                            class="block w-full pl-12 pr-12 py-4 text-sm border border-gray-200 rounded-xl bg-gray-50 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:bg-white transition-all duration-200 shadow-sm hover:border-gray-300"
                            placeholder="Suche nach ID, Name, Chargennummer, Teilenummer, Eisenmarke..."
                        >
                        <button
                            v-if="search"
                            @click="clearSearch"
                            class="absolute inset-y-0 right-0 pr-4 flex items-center cursor-pointer text-gray-400 hover:text-gray-600 transition-colors"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Modern Filters -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <!-- Status Filter -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select
                            id="status"
                            v-model="selectedStatus"
                            class="block w-full px-4 py-3 text-sm border border-gray-200 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:bg-white transition-all duration-200 shadow-sm hover:border-gray-300"
                        >
                            <option v-for="option in statusOptions" :key="option.value" :value="option.value">
                                {{ option.label }}
                            </option>
                        </select>
                    </div>

                    <!-- Abteilung Filter -->
                    <div>
                        <label for="abteilung" class="block text-sm font-medium text-gray-700 mb-2">Abteilung</label>
                        <select
                            id="abteilung"
                            v-model="selectedAbteilung"
                            class="block w-full px-4 py-3 text-sm border border-gray-200 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:bg-white transition-all duration-200 shadow-sm hover:border-gray-300"
                        >
                            <option v-for="option in abteilungOptions" :key="option.value" :value="option.value">
                                {{ option.label }}
                            </option>
                        </select>
                    </div>

                    <!-- Date Range Filters -->
                    <div>
                        <label for="date-from" class="block text-sm font-medium text-gray-700 mb-2">Datum von</label>
                        <input
                            type="date"
                            id="date-from"
                            v-model="dateFrom"
                            class="block w-full px-4 py-3 text-sm border border-gray-200 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:bg-white transition-all duration-200 shadow-sm hover:border-gray-300"
                        >
                    </div>

                    <div>
                        <label for="date-to" class="block text-sm font-medium text-gray-700 mb-2">Datum bis</label>
                        <input
                            type="date"
                            id="date-to"
                            v-model="dateTo"
                            class="block w-full px-4 py-3 text-sm border border-gray-200 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 focus:bg-white transition-all duration-200 shadow-sm hover:border-gray-300"
                        >
                    </div>
                </div>

                <!-- Filter Actions -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button
                            @click="resetFilters"
                            class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 transition-colors"
                        >
                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Filter zurücksetzen
                        </button>
                    </div>
                    <div class="text-sm text-gray-500">
                        {{ rotekarten.from }}-{{ rotekarten.to }} von {{ rotekarten.total }} Einträgen
                    </div>
                </div>
            </div>

            <HBWTableModal
                :show="showHBWTable"
                @close="showHBWTable = false"
            />

            <!-- Modern Data Table -->
            <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
                <div v-if="rotekarten.data.length === 0" class="p-12">
                    <div class="text-center">
                        <div class="mx-auto w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mb-6">
                            <svg class="h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Keine Rotekarten gefunden</h3>
                        <p class="text-gray-600 mb-6">Es wurden keine Rotekarten gefunden, die Ihren Suchkriterien entsprechen.</p>
                        <Link
                            :href="route('spektrometer.index')"
                            class="inline-flex items-center px-6 py-3 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200"
                        >
                            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            Erste Rotekarte erstellen
                        </Link>
                    </div>
                </div>

                <div v-else class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead class="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                            <tr>
                                <th class="px-6 py-6 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider w-24">
                                    <div class="flex items-center space-x-1">
                                        <span>ID</span>
                                        <svg class="h-3 w-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                                        </svg>
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                                    <div class="flex items-center space-x-1">
                                        <span>Rotekarten Details</span>
                                        <svg class="h-3 w-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                        </svg>
                                    </div>
                                </th>
                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider w-72">
                                    <div class="flex items-center space-x-1">
                                        <span>Aktionen & Status</span>
                                        <svg class="h-3 w-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                                        </svg>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-100">
                            <tr v-for="rotekarte in rotekarten.data" :key="rotekarte.id" :class="{
                                'bg-gradient-to-r from-yellow-50 to-amber-50 border-l-4 border-yellow-400': rotekarte.type === 'yellow',
                                'bg-gradient-to-r from-red-50 to-rose-50 border-l-4 border-red-400': rotekarte.type === 'red' && rotekarte.qs_daten?.status === 'Abgeschlossen',
                                'hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 transition-all duration-200': !(rotekarte.type === 'yellow' || (rotekarte.type === 'red' && rotekarte.qs_daten?.status === 'Abgeschlossen'))
                            }" class="group">
                                <td class="px-6 py-6 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                                            <span class="text-white font-bold text-sm">RT</span>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-lg font-bold text-gray-900">{{ rotekarte.id }}</div>
                                            <div class="text-xs text-gray-500">{{ new Date(rotekarte.created_at).toLocaleDateString('de-DE') }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="space-y-4">
                                        <!-- Spektrometer Data Section -->
                                        <div v-if="rotekarte.spektrometer_daten" class="space-y-2">
                                            <div class="grid grid-cols-2 gap-4">
                                                <!-- Name und Uhrzeit -->
                                                <div class="space-y-2">
                                                    <div>
                                                        <span class="text-xs text-gray-500">Element</span>
                                                        <p class="text-sm font-medium text-gray-900">
                                                            {{ rotekarte.spektrometer_daten?.analysewerte?.[0]?.element }}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <span class="text-xs text-gray-500">Uhrzeit</span>
                                                        <p class="text-sm font-medium text-gray-900">{{ rotekarte.spektrometer_daten.uhrzeit }}</p>
                                                    </div>
                                                </div>

                                                <!-- Ist-Wert und Datum -->
                                                <div class="space-y-2">
                                                    <div>
                                                        <span class="text-xs text-gray-500">Ist-Wert</span>
                                                        <p class="text-sm font-medium text-gray-900">
                                                            {{ rotekarte.spektrometer_daten?.analysewerte?.[0]?.istWert }}
                                                        </p>
                                                    </div>
                                                    <div>
                                                        <span class="text-xs text-gray-500">Datum</span>
                                                        <p class="text-sm font-medium text-gray-900">{{ formatDate(rotekarte.spektrometer_daten.datum) }}</p>
                                                    </div>
                                                </div>

                                                <!-- Chargennummer -->
                                                <div>
                                                    <span class="text-xs text-gray-500">Chargennummer</span>
                                                    <p class="text-sm font-medium text-gray-900">{{ rotekarte.spektrometer_daten.chargennummer }}</p>
                                                </div>

                                                <!-- QS Abteilung -->
                                                <div>
                                                    <span class="text-xs text-gray-500">QS Abteilung</span>
                                                    <p class="text-sm font-medium text-gray-900">
                                                        {{ ['GG', 'HF'].includes(rotekarte.spektrometer_daten.abteilung) ? 'Großguss' : 'Kleinguss' }}
                                                    </p>
                                                </div>

                                                <!-- Eisenmarke -->
                                                <div>
                                                    <span class="text-xs text-gray-500">Eisenmarke</span>
                                                    <p class="text-sm font-medium text-gray-900">
                                                        {{ rotekarte.spektrometer_daten.eisenmarke }}
                                                    </p>
                                                </div>

                                                <!-- SAP-Zahlnummer -->
                                                <div>
                                                    <span class="text-xs text-gray-500">SAP-Zahlnummer</span>
                                                    <p class="text-sm font-medium text-gray-900">
                                                        {{ rotekarte.formanlage_daten?.sap_zahlnummer }}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- QS Data Section -->
                                        <div v-if="rotekarte.qs_daten" class="space-y-2 border-t pt-2">
                                            <div class="grid grid-cols-4 gap-4">
                                                <!-- Serie Status -->
                                                <div>
                                                    <span class="text-xs text-gray-500">Abnahmebeauftragter</span>
                                                    <p class="text-sm font-medium mt-1" :class="{
                                                        'text-orange-600': rotekarte.qs_daten.serie_status === 'Entscheidung ausstehend',
                                                        'text-green-600': rotekarte.qs_daten.serie_status === 'Teile in die Serie einreihen',
                                                        'text-red-600': rotekarte.qs_daten.serie_status === 'Zur Verschrottung freigegeben'
                                                    }">{{ rotekarte.qs_daten.serie_status }}</p>
                                                </div>

                                                <!-- Status und Vorschlag Prüfungen -->
                                                <div class="col-span-1">
                                                    <span class="text-xs text-gray-500">QS-Status</span>
                                                    <div class="flex items-center space-x-2 mt-1">
                                                        <span :class="{
                                                            'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium': true,
                                                            'bg-yellow-100 text-yellow-800': rotekarte.qs_daten?.status === 'In Bearbeitung',
                                                            'bg-green-100 text-green-800': rotekarte.qs_daten?.status === 'Abgeschlossen',
                                                            'bg-red-100 text-red-800': rotekarte.qs_daten?.status === 'Abgelehnt'
                                                        }">
                                                            {{ rotekarte.qs_daten?.status }}
                                                        </span>
                                                    </div>
                                                </div>

                                                <!-- Maßnahmen -->
                                                <div class="col-span-1">
                                                    <span class="text-xs text-gray-500">Maßnahmen</span>
                                                    <div class="flex items-center mt-1">
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                            <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                                            </svg>
                                                            {{ rotekarte.qs_daten?.vorschlag_pruefungen || 'Keine' }}
                                                        </span>
                                                    </div>
                                                </div>

                                                <!-- Erstellt am -->
                                                <div>
                                                    <span class="text-xs text-gray-500">Erstellt am</span>
                                                    <p class="text-sm font-medium text-gray-900 mt-1">
                                                        {{ new Date(rotekarte.created_at).toLocaleString('de-DE') }}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex flex-col space-y-2">
                                        <Link
                                            :href="route('rotekarte.show', rotekarte.id)"
                                            class="inline-flex items-center text-sm text-indigo-600 hover:text-indigo-900"
                                        >
                                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                            Details
                                        </Link>

                                        <!-- Formanlage Link -->
                                        <Link
                                            v-if="!rotekarte.formanlage_daten"
                                            :href="route('formanlage.index', { rotekarte: rotekarte.id })"
                                            class="inline-flex items-center text-sm text-green-600 hover:text-green-900"
                                        >
                                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                            </svg>
                                            Zur Formanlage
                                        </Link>

                                        <!-- Gussnachbehandlung Link -->
                                        <Link
                                            v-if="rotekarte.formanlage_daten && !rotekarte.gussnachbehandlung_daten"
                                            :href="route('gussnachbehandlung.index', { rotekarte: rotekarte.id })"
                                            class="inline-flex items-center text-sm text-yellow-600 hover:text-yellow-900"
                                        >
                                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2M7 7h10" />
                                            </svg>
                                            Zur Gussnachbehandlung
                                        </Link>

                                        <!-- QS Links -->
                                        <template v-if="rotekarte.formanlage_daten">
                                            <Link
                                                v-if="!rotekarte.qs_daten"
                                                :href="route('qs.index', { rotekarte: rotekarte.id })"
                                                class="inline-flex items-center text-sm text-purple-600 hover:text-purple-900"
                                            >
                                                <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                                                </svg>
                                                Zum QS
                                            </Link>
                                            <Link
                                                v-else
                                                :href="route('qs.index', { rotekarte: rotekarte.id })"
                                                class="inline-flex items-center text-sm text-purple-600 hover:text-purple-900"
                                            >
                                                <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                </svg>
                                                QS Bearbeiten
                                            </Link>
                                        </template>
                                        <!-- Gelbe Karte Link -->
                                        <a v-if="rotekarte.type === 'yellow'"
                                            :href="`/storage/GelbeKarte/Gelbkarte-${rotekarte.id}.pdf`"
                                            target="_blank"
                                            class="inline-flex items-center text-sm text-gray-600 hover:text-gray-900"
                                        >
                                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                            </svg>
                                            PDF Gelbe Karte
                                        </a>
                                        <!-- PDF Link -->
                                        <a
                                            v-if="rotekarte.formanlage_daten"
                                            :href="`/storage/rotekarten/rotekarte-${rotekarte.id}.pdf`"
                                            target="_blank"
                                            class="inline-flex items-center text-sm text-gray-600 hover:text-gray-900"
                                        >
                                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                            </svg>
                                            PDF Rotekarte 1
                                        </a>
                                        <!-- Final PDF Rotekarte Link -->
                                        <a
                                            v-if="rotekarte.status === 'Abgeschlossen'"
                                            :href="`/storage/RotekarteFinal/Rotekarte-${rotekarte.id}-Final.pdf`"
                                            target="_blank"
                                            class="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 mt-2"
                                        >
                                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                            PDF Rotekarte Final
                                        </a>

                                        <!-- Teile anzeigen Button -->
                                        <button
                                            v-if="rotekarte.qs_daten?.status === 'Abgeschlossen' ? rotekarte.qs_daten?.teile : rotekarte.formanlage_daten?.teile"
                                            @click="openTeileModal(rotekarte.qs_daten?.status === 'Abgeschlossen' ? rotekarte.qs_daten.teile : rotekarte.formanlage_daten.teile)"
                                            class="inline-flex items-center text-sm text-blue-600 hover:text-blue-900"
                                        >
                                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
                                            </svg>
                                            {{ rotekarte.qs_daten?.status === 'Abgeschlossen' ? 'Teile anzeigen' : 'Teile anzeigen' }}
                                        </button>
                                                                                <!-- Prüfmethoden ermitteln Button -->
                                                                                <Link
                                            v-if="rotekarte.spektrometer_daten?.analysewerte?.length > 0"
                                            :href="route('rotekarte.show', rotekarte.id) + '?openAiModal=true'"
                                            class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-md text-white text-xs font-medium shadow-sm hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150 mt-2"
                                        >
                                            <svg class="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                            </svg>
                                            Prüfmethoden ermitteln
                                        </Link>

                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Modern Pagination -->
                    <div class="px-6 py-6 bg-gradient-to-r from-gray-50 to-gray-100 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <!-- Mobile Pagination -->
                            <div class="flex-1 flex justify-between sm:hidden">
                                <Link
                                    v-if="rotekarten.prev_page_url"
                                    :href="rotekarten.prev_page_url"
                                    class="relative inline-flex items-center px-6 py-3 border border-gray-300 text-sm font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 shadow-sm transition-all duration-200"
                                >
                                    <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                                    </svg>
                                    Zurück
                                </Link>
                                <Link
                                    v-if="rotekarten.next_page_url"
                                    :href="rotekarten.next_page_url"
                                    class="relative inline-flex items-center px-6 py-3 border border-gray-300 text-sm font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 shadow-sm transition-all duration-200"
                                >
                                    Weiter
                                    <svg class="h-4 w-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                    </svg>
                                </Link>
                            </div>

                            <!-- Desktop Pagination -->
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="p-2 bg-blue-50 rounded-lg">
                                        <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-700">
                                            Zeige <span class="font-bold text-indigo-600">{{ rotekarten.from }}</span> bis
                                            <span class="font-bold text-indigo-600">{{ rotekarten.to }}</span> von
                                            <span class="font-bold text-indigo-600">{{ rotekarten.total }}</span> Rotekarten
                                        </p>
                                        <p class="text-xs text-gray-500 mt-1">
                                            Seite {{ rotekarten.current_page }} von {{ rotekarten.last_page }}
                                        </p>
                                    </div>
                                </div>

                                <div class="flex items-center space-x-2">
                                    <Link
                                        v-if="rotekarten.prev_page_url"
                                        :href="rotekarten.prev_page_url"
                                        class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 shadow-sm transition-all duration-200 hover:shadow-md"
                                    >
                                        <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                                        </svg>
                                        Zurück
                                    </Link>

                                    <Link
                                        v-if="rotekarten.next_page_url"
                                        :href="rotekarten.next_page_url"
                                        class="relative inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 shadow-sm transition-all duration-200 hover:shadow-md"
                                    >
                                        Weiter
                                        <svg class="h-4 w-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- TeileModal component -->
            <TeileModal
                :show="showTeileModal"
                :teile="selectedTeile"
                @close="showTeileModal = false"
            />
        </div>
    </AppLayout>
</template>
